using PosGTech.ModelsDTO.Items;
using PosGTech.Models.ViewModels;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IPaginatedItemService
    {
        Task<(PaginatedItemsDTO? result, ResponseVM? error)> SearchItemsForPurchaseAsync(string? search = null, int page = 1, int pageSize = 50);
        Task<(IEnumerable<object>? suggestions, ResponseVM? error)> QuickSearchAsync(string? search = null, int limit = 10);
        Task<(IEnumerable<object>? items, ResponseVM? error)> GetPopularItemsAsync(int limit = 20);
        Task PreloadPopularItemsAsync();
    }
}
