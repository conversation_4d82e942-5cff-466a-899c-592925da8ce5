﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Units;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ItemsController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllItemsCM")]
        public async Task<IActionResult> GetAllItemsCM()
        {
            var items = maper.Map<IEnumerable<Item>, IEnumerable<ItemCMDTO>>(await unitOfWork.Item.GetAll());
            return Ok(items);
        }
        [HttpGet("getAllItems")]
        public async Task<IActionResult> GetAllItems()
        {
            var items = maper.Map<IEnumerable<Item>, IEnumerable<ItemDTO>>(await unitOfWork.Item.GetAll(includeProperties: "ItemNums,ItemUnits.Unit,StoreItems.StoreItemExps"));
            return Ok(items);
        }
        [HttpGet("getAllItemsForPurchase")]
        public async Task<IActionResult> GetAllItemsForPurchase()
        {
            var items = maper.Map<IEnumerable<Item>, IEnumerable<ItemDTO>>(await unitOfWork.Item.GetAll(includeProperties: "ItemNums,ItemUnits.Unit", order: x => x.UpdatedAt));
            return Ok(items);
        }

        [HttpGet("searchItemsForPurchase")]
        public async Task<IActionResult> SearchItemsForPurchase(
            [FromQuery] string? search = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                // التحقق من صحة المعاملات
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 50;

                // إنشاء فلتر البحث
                System.Linq.Expressions.Expression<Func<Item, bool>>? filter = null;
                if (!string.IsNullOrWhiteSpace(search))
                {
                    var searchTerm = search.Trim().ToLower();
                    filter = item =>
                        item.Name.ToLower().Contains(searchTerm) ||
                        item.ItemNums.Any(num => num.Barcode.ToLower().Contains(searchTerm));
                }

                // جلب العدد الإجمالي للأصناف
                var totalItems = await unitOfWork.Item.GetAll(filter: filter);
                var totalCount = totalItems.Count();

                // جلب الأصناف مع الترقيم
                var items = await unitOfWork.Item.GetAll(
                    filter: filter,
                    includeProperties: "ItemNums,ItemUnits.Unit",
                    order: x => x.UpdatedAt,
                    skip: (page - 1) * pageSize,
                    take: pageSize);

                var itemDTOs = maper.Map<IEnumerable<Item>, IEnumerable<ItemDTO>>(items);

                // إرجاع النتيجة مع معلومات الترقيم
                var result = new
                {
                    Items = itemDTOs,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                    HasNextPage = page * pageSize < totalCount,
                    HasPreviousPage = page > 1
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "حدث خطأ أثناء البحث عن الأصناف", Error = ex.Message });
            }
        }

        [HttpGet("getAllItemsByStore")]
        public async Task<IActionResult> GetAllItemsByStore([FromQuery] Guid? storeId = null)
        {
            if (storeId == null || storeId == Guid.Empty)
            {
                // إرجاع جميع الأصناف مع الكميات الإجمالية من جميع المخازن
                var allItems = maper.Map<IEnumerable<Item>, IEnumerable<ItemDTO>>(
                    await unitOfWork.Item.GetAll(includeProperties: "ItemNums,ItemUnits.Unit,StoreItems.StoreItemExps"));
                return Ok(allItems);
            }
            else
            {
                // جلب الأصناف التي لها بيانات في المخزن المحدد
                var items = await unitOfWork.Item.GetAll(
                    filter: item => item.StoreItems.Any(si => si.StoreId == storeId),
                    includeProperties: "ItemNums,ItemUnits.Unit,StoreItems.StoreItemExps");

                // حساب الكميات في الذاكرة
                var storeItems = items
                    .Select(item => new ItemDTO
                    {
                        Id = item.Id,
                        Name = item.Name,
                        CategoryId = item.CategoryId,
                        CostPrice = item.CostPrice,
                        IsHaveExp = item.IsHaveExp,
                        Quantity = item.StoreItems
                            .Where(si => si.StoreId == storeId)
                            .SelectMany(si => si.StoreItemExps ?? new List<StoreItemExp>())
                            .Sum(sie => sie.Quantity),
                        ItemNums = item.ItemNums.Select(num => new ItemNumDTO
                        {
                            Id = num.Id,
                            Barcode = num.Barcode
                        }).ToList(),
                        ItemUnits = item.ItemUnits.Select(unit => new ItemUnitDTO
                        {
                            Id = unit.Id,
                            IsBasicUnit = unit.IsBasicUnit,
                            IsBigger = unit.IsBigger,
                            Quantity = unit.Quantity,
                            SalePrice = unit.SalePrice, // إضافة سعر البيع
                            Unit = new UnitDTO
                            {
                                Id = unit.Unit.Id,
                                Name = unit.Unit.Name
                            }
                        }).ToList()
                    })
                    .Where(item => item.Quantity > 0) // عرض الأصناف التي لها كمية فقط
                    .ToList();

                return Ok(storeItems);
            }
        }

        [HttpGet("getItemById/{id:Guid}")]
        public async Task<IActionResult> GetItemById([FromRoute] Guid id)
        {
            //"ItemNums,BasicUnit,Category"
            var item = await unitOfWork.Item.GetItemDTOById(id);// maper.Map<Item, ItemDTO>(await unitOfWork.Item.GetFirstOrDefault(x => x.Id == id, "ItemNums,ItemUnits,ItemUnits.Unit", false));
            return Ok(item);
        }



        [HttpPost("insertItem")]
        public async Task<IActionResult> InsertItem([FromBody] ItemDTO model)
        {
            if (!ModelState.IsValid)
                return Ok(new ResponseVM() { State = false, Message = $"الرجاء تعبئة البيانات بشكل صحيح" });
            var newItem = maper.Map<Item>(model);
            unitOfWork.Item.Add(newItem);

            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(new ResponseVM() { State = true, Message = newItem.Id.ToString() });
            else return BadRequest(res);

        }




        [HttpPut("updateItem/{id:Guid}")]
        public async Task<IActionResult> UpdateItem([FromRoute] Guid id, [FromBody] ItemDTO model)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(new ResponseVM() { State = false, Message = "الرجاء تعبئة البيانات بشكل صحيح" });

                var oldItem = await unitOfWork.Item.GetFirstOrDefault(x => x.Id == id, "ItemNums,ItemUnits");

                if (oldItem == null) return BadRequest(new ResponseVM() { Message = "  الصنف المراد التعديل عليه غير موجود " });
                unitOfWork.Item.UpdateItem(oldItem, model);

                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {

                throw ex;
            }



        }


        [HttpDelete("deleteItem/{id:Guid}")]
        public async Task<IActionResult> DeleteItem([FromRoute] Guid id)
        {
            var olditem = await unitOfWork.Item.GetByIdAsync(id);
            if (olditem == null) return BadRequest(new ResponseVM() { Message = " الصنف المراد حذفه  غير موجود" });
            unitOfWork.Item.Remove(olditem);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
    }
}
