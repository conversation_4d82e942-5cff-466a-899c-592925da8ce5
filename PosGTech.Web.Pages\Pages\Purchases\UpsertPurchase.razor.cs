using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using MudBlazor;
using MudExtensions;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO;
using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Purchases;
using PosGTech.ModelsDTO.ShopSettings;
using PosGTech.ModelsDTO.Stores;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.Web.Pages.Pages.Clients;
using PosGTech.Web.Pages.Pages.Items;
using PosGTech.Web.Pages.Pages.Receipts;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Purchases;

public partial class UpsertPurchase
{
    [CascadingParameter] MudDialogInstance MudDialog { get; set; }
    [Inject]
    IDialogService DialogService { get; set; }
    [Parameter]
    public Guid id { get; set; }
    PurchaseDTO purchase = new();
    PurchaseItemDTO selectedPurchaseItem = new PurchaseItemDTO();
    List<ItemDTO> items = new List<ItemDTO>();
    List<UserTreasuryCMDTO> userTreasuries = new List<UserTreasuryCMDTO>();
    List<ClientCMDTO> clients = new List<ClientCMDTO>();
    List<StoreCMDTO> stores = new List<StoreCMDTO>();
    List<PurchasesNumDTO> PurchasesNum = new List<PurchasesNumDTO>();
    [Inject]
    IGRepository<PurchaseDTO> _purchase { get; set; }
    [Inject]
    IGRepository<PurchasesNumDTO> _purchasesNum { get; set; }
    [Inject]
    IGRepository<UserTreasuryCMDTO> _userTreasury { get; set; }
    [Inject]
    IGRepository<ItemDTO> _item { get; set; }
    [Inject]
    AuthenticationStateProvider _auth { get; set; }
    [Inject]
    IGRepository<ClientCMDTO> _client { get; set; }
    [Inject]
    IGRepository<StoreCMDTO> _store { get; set; }
    [Inject]
    IJSRuntime _iJSRuntime { get; set; }
    [Inject]
    IGRepository<ShopSettingsDTO> _shopSettings { get; set; }
    [Inject]
    NavigationManager Navigation { get; set; }
    MudMessageBox mbox { get; set; }
    DateTime? _datePurchase = DateTime.Now, _dateExp = null;
    decimal SalePriceChange = 0;
    string _message; int index = -2; bool _isDeleteMessage = false;
    EditForm Form { get; set; }
    MudAutocomplete<ItemCMDTO> ItemForAdd { get; set; }
    MudSelect<ItemUnitDTO> ItemUnit { get; set; }
    MudTextField<int> InvoiceNo { get; set; }

    // متغيرات حالة التحميل للأزرار
    private bool _isLoading = false;
    private bool _isSaveLoading = false;
    private bool _isPrintLoading = false;
    private bool _isDeleteLoading = false;
    private bool _isNewLoading = false;
    private bool _isReceiptLoading = false;

    // متغيرات الطباعة وإعدادات المتجر
    private ShopSettingsDTO? _defaultShopSettings;
    private bool _isPrinting = false;
    private string _printButtonText = "طباعة الفاتورة";

    // طرق مساعدة لإدارة حالة التحميل
    private void SetLoadingState(bool isLoading)
    {
        _isLoading = isLoading;
        StateHasChanged();
    }

    private void SetButtonLoadingState(string buttonType, bool isLoading)
    {
        switch (buttonType.ToLower())
        {
            case "save":
                _isSaveLoading = isLoading;
                break;
            case "print":
                _isPrintLoading = isLoading;
                break;
            case "delete":
                _isDeleteLoading = isLoading;
                break;
            case "new":
                _isNewLoading = isLoading;
                break;
            case "receipt":
                _isReceiptLoading = isLoading;
                break;
        }
        _isLoading = _isSaveLoading || _isPrintLoading || _isDeleteLoading || _isNewLoading || _isReceiptLoading;
        StateHasChanged();
    }

    private void ShowSuccessMessage(string message = "تمت العملية بنجاح")
    {
        _snackbar.Add(message, Severity.Success);
    }

    private void ShowErrorMessage(string message = "حدث خطأ أثناء تنفيذ العملية")
    {
        _snackbar.Add(message, Severity.Error);
    }
    public async void keydownForm(KeyboardEventArgs args)
    {
        if (args.Key == "F2") await Upsert();
        if (args.Key == "F8") await NewPurchase();
    }
    public async Task KeyDownInvoice(KeyboardEventArgs args)
    {
        // نفترض وجود خاصية id التي تحمل معرف الفاتورة الحالية.
        // وتوجد مجموعة PurchasesNum تحتوي على الفواتير بخصائص InvoiceNo و Id.

        if (args.Key == "Enter")
        {
            // التأكد من أن القيمة محدثة قبل البحث
            StateHasChanged();
            await Task.Delay(100); // إعطاء وقت قصير لتحديث القيمة

            // جلب الفاتورة بناءً على رقم الفاتورة المدخل
            var purchaseFound = PurchasesNum.FirstOrDefault(x => x.InvoiceNo == purchase.InvoiceNo);
            if (purchaseFound == null)
            {
                _snackbar.Add("الفاتورة غير موجودة", Severity.Error);
                return;
            }
            await GetPurchase(purchaseFound.Id);
            await InvoiceNo.FocusAsync();
        }
        else if (args.Key == "ArrowRight")
        {
            // عند الضغط على السهم اليمين:
            // إذا كان id ليس Guid.Empty نحاول جلب الفاتورة السابقة.
            if (id != Guid.Empty)
            {
                int currentIndex = PurchasesNum.FindIndex(x => x.Id == id);
                if (currentIndex > 0)
                {
                    // الفاتورة السابقة تكون العنصر الذي قبله في القائمة
                    var previousInvoice = PurchasesNum[currentIndex - 1];
                    await GetPurchase(previousInvoice.Id);
                }
                else
                {
                    _snackbar.Add("لا توجد فاتورة سابقة", Severity.Error);
                }
            }
            else
            {
                // إذا كان id يساوي Guid.Empty، نجلب آخر فاتورة إن وجدت
                if (PurchasesNum.Any())
                {
                    var lastInvoice = PurchasesNum.Last();
                    await GetPurchase(lastInvoice.Id);
                }
                else
                {
                    _snackbar.Add("لا توجد فواتير", Severity.Error);
                }
            }
            await InvoiceNo.FocusAsync();
        }
        else if (args.Key == "ArrowLeft")
        {
            // عند الضغط على السهم اليسار:
            // إذا كان id ليس Guid.Empty نحاول جلب الفاتورة اللاحقة.
            if (id != Guid.Empty)
            {
                int currentIndex = PurchasesNum.FindIndex(x => x.Id == id);
                if (currentIndex >= 0 && currentIndex < PurchasesNum.Count - 1)
                {
                    // الفاتورة اللاحقة تكون العنصر الذي يلي الفاتورة الحالية
                    var nextInvoice = PurchasesNum[currentIndex + 1];
                    await GetPurchase(nextInvoice.Id);
                }
                else
                {
                    _snackbar.Add("لا توجد فاتورة لاحقة", Severity.Error);
                }
            }
            else
            {
                // إذا كان id يساوي Guid.Empty، نجلب آخر فاتورة إن وجدت
                if (PurchasesNum.Any())
                {
                    var lastInvoice = PurchasesNum.Last();
                    await GetPurchase(lastInvoice.Id);
                }
                else
                {
                    _snackbar.Add("لا توجد فواتير", Severity.Error);
                }
            }
            await InvoiceNo.FocusAsync();
        }
    }
    public async Task SelectItem(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            if (ItemForAdd.Text == "" || ItemForAdd.Text is null)
                return;
            if (items.Any(x => x.Name.Contains(ItemForAdd.Text, StringComparison.InvariantCultureIgnoreCase) || x.ItemNums?.Any(x => x.Barcode == ItemForAdd.Text) == true))
            {
                await ChangeItem(items.Where(x => x.Name.Contains(ItemForAdd.Text, StringComparison.InvariantCultureIgnoreCase) || x.ItemNums?.Any(x => x.Barcode == ItemForAdd.Text) == true).Select(x => new ItemCMDTO() { Id = x.Id, Name = x.Name }).First());

            }
            else await ChangeItem(null);
            StateHasChanged();
            await ItemUnit.FocusAsync();
            return;
        }
        else if (!(e.Key == "ArrowDown") && !(e.Key == "ArrowUp"))
        {
            // MudAutocomplete يتعامل مع التنقل تلقائياً
            // لا نحتاج لاستدعاء ActivateFirstItem
        }
    }

    async Task GetItems()
    {

        var resItem = await _item.GetAll("Items/getAllItemsForPurchase");
        if (resItem.response == null) items = resItem.list.ToList();
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
        StateHasChanged();
    }
    async Task GetClients()
    {

        var resClient = await _client.GetAll("Clients/getAllSuppliers");
        if (resClient.response == null) clients = resClient.list.ToList();
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
    }
    async Task GetPurchasesNum()
    {
        var res = await _purchasesNum.GetAll("Purchases/getAllPurchasesNum");
        if (res.response == null) PurchasesNum = res.list.ToList();
    }
    async Task GetPurchase(Guid purchaseId)
    {
        id = purchaseId;
        var res = await _purchase.GetByIdAsync("Purchases/getPurchaseById", purchaseId);
        if (res.response == null)
        {
            purchase = res.model;
            _datePurchase = purchase.Date.ToDateTime(time: TimeOnly.MinValue);
            StateHasChanged();
        }
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
    }
    protected override async Task OnInitializedAsync()
    {
        var resStore = await _store.GetAll("Stores/getAllStoresCMB");
        if (resStore.response == null) stores = resStore.list.ToList();
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
        await GetItems();
        await GetPurchasesNum();
        await GetClients();
        await LoadDefaultShopSettings();
        if (id != Guid.Empty) await GetPurchase(id);
        else
        {
            //purchase.InvoiceNo = PurchasesNum.Last().InvoiceNo + 1;
            var auth = await _auth.GetAuthenticationStateAsync();
            var resUserTreasuries = await _userTreasury.GetAll($"Users/getAllTreasuriesForUser/{auth.User.Claims.First(x => x.Type == "id").Value}");
            if (resUserTreasuries.response == null) userTreasuries = resUserTreasuries.list.ToList();
            else
            {
                _snackbar.Add("خطأ في الاتصال", Severity.Error);
                MudDialog.Cancel();
            }
        }
    }
    async Task NewPurchase()
    {
        if (_isLoading) return; // منع التنفيذ المتعدد

        try
        {
            SetButtonLoadingState("new", true);

            purchase = new();
            _datePurchase = DateTime.Now;
            id = new();

            ShowSuccessMessage("تم إنشاء فاتورة شراء جديدة");
        }
        catch (Exception ex)
        {
            ShowErrorMessage("حدث خطأ أثناء إنشاء فاتورة شراء جديدة. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            SetButtonLoadingState("new", false);
        }
    }
    async Task Upsert()
    {
        if (_isLoading) return; // منع التنفيذ المتعدد

        try
        {
            SetButtonLoadingState("save", true);

            if (!Form.EditContext.Validate()) return;
            if (purchase.PurchaseItemDTOs.Count == 0)
            {
                ShowErrorMessage("يرجى اضافة اصناف");
                return;
            }
            if (_datePurchase == null)
            {
                ShowErrorMessage("يرجى اختيار تاريخ");
                return;
            }
            if (purchase.TreasuryId == null && purchase.Paid > 0 && purchase.Id == Guid.Empty)
            {
                ShowErrorMessage("يرجى اختيار الخزينة");
                return;
            }

            purchase.Date = DateOnly.FromDateTime(_datePurchase.Value);
            ResponseVM response;

            if (id == Guid.Empty)
            {
                response = await _purchase.Insert("Purchases/insertPurchase", purchase);
            }
            else
            {
                response = await _purchase.Update("Purchases/updatePurchase", purchase, id);
            }

            if (response.State)
            {
                ShowSuccessMessage("تم الحفظ بنجاح");
                await GetPurchase(id == Guid.Empty ? Guid.Parse(response.Message) : id);
                await GetPurchasesNum();
            }
            else
            {
                ShowErrorMessage(response.Message);
            }
        }
        catch (Exception ex)
        {
            // يمكن إضافة تسجيل الأخطاء هنا إذا كان متوفراً
            ShowErrorMessage("حدث خطأ أثناء حفظ فاتورة الشراء. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            SetButtonLoadingState("save", false);
        }
    }
    void Back() => MudDialog.Cancel();

    private async Task<IEnumerable<ItemCMDTO>> SearchItem(string value, CancellationToken token)
    {
        // إذا كان البحث فارغ، إرجاع أول 50 عنصر
        if (string.IsNullOrWhiteSpace(value))
            return items.Take(50).Select(x => new ItemCMDTO() { Id = x.Id, Name = x.Name, ItemNums = x.ItemNums });
            
        // السماح بالبحث بحرف واحد فقط للأرقام (باركود)
        // للنصوص العادية، نطلب حرفين على الأقل
        if (value.Length == 1)
        {
            // إذا كان الحرف رقم، ابحث في الباركود
            if (char.IsDigit(value[0]))
            {
                try
                {
                    var barcodeResults = items
                        .Where(x => x.ItemNums?.Any(num => num.Barcode.StartsWith(value)) == true)
                        .Take(20)
                        .Select(x => new ItemCMDTO() { Id = x.Id, Name = x.Name, ItemNums = x.ItemNums })
                        .ToList();
                        
                    await Task.Delay(100, token);
                    return barcodeResults;
                }
                catch (OperationCanceledException)
                {
                    return Enumerable.Empty<ItemCMDTO>();
                }
            }
            else
            {
                // للنصوص بحرف واحد، لا نرجع نتائج (ننتظر حرف إضافي)
                return Enumerable.Empty<ItemCMDTO>();
            }
        }

        try
        {
            var searchResults = items
                .Where(x => 
                    x.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase) || 
                    x.ItemNums?.Any(num => num.Barcode.Contains(value, StringComparison.InvariantCultureIgnoreCase)) == true
                )
                .Take(100) // تحديد عدد النتائج لتحسين الأداء
                .Select(x => new ItemCMDTO() { Id = x.Id, Name = x.Name, ItemNums = x.ItemNums })
                .ToList();
                
            // تأخير قصير لتجنب البحث المتكرر السريع
            await Task.Delay(100, token);
            
            return searchResults;
        }
        catch (OperationCanceledException)
        {
            return Enumerable.Empty<ItemCMDTO>();
        }
        catch
        {
            // في حالة الخطأ، أرجع قائمة افتراضية
            return items.Take(50).Select(x => new ItemCMDTO() { Id = x.Id, Name = x.Name, ItemNums = x.ItemNums });
        }
    }

    Func<ClientCMDTO, string, string, bool> SearchClientFunc => SearchClient;

    private bool SearchClient(ClientCMDTO cMDTO, string arg2, string arg3)
    {
        return cMDTO.Name.Contains(arg3, StringComparison.InvariantCultureIgnoreCase) || string.IsNullOrEmpty(arg3);
    }


    private async void AddItemPurchase()
    {
        if (selectedPurchaseItem.Item == null)
        { _snackbar.Add($"يرجى اختيار صنف", Severity.Error); return; }
        if (_dateExp is not null)
        {
            if (_dateExp < DateTime.Now)
            {
                _isDeleteMessage = false;
                _message = $"الصلاحية {_dateExp.Value.ToShortDateString()} منتهية هل تريد الإستمرار؟";
                bool? result = await mbox.ShowAsync();
                if (result != true) return;
            }
            selectedPurchaseItem.Exp = DateOnly.FromDateTime(_dateExp.Value);
        }
        if (SalePriceChange != selectedPurchaseItem.ItemUnit.SalePrice)
        {
            if (!purchase.SalePriceChanges.Any(x => x.ItemId == selectedPurchaseItem.Item.Id))
            {
                _isDeleteMessage = false;
                _message = $"سعر البيع تم تغييره هل تريد تغييره في جميع الوحدات؟";
                bool? result = await mbox.ShowAsync();
                if (result == true)
                {
                    var sellprice = selectedPurchaseItem.ItemUnit.IsBasicUnit ? SalePriceChange : selectedPurchaseItem.ItemUnit.IsBigger ? SalePriceChange / selectedPurchaseItem.ItemUnit.Quantity : SalePriceChange * selectedPurchaseItem.ItemUnit.Quantity;
                    foreach (var unit in items.First(x => x.Id == selectedPurchaseItem.Item.Id).ItemUnits)
                    {
                        if (purchase.SalePriceChanges.Any(x => x.ItemId == selectedPurchaseItem.Item.Id && x.ItemUnitId == unit.Id))
                        {
                            purchase.SalePriceChanges.First(x => x.ItemId == selectedPurchaseItem.Item.Id && x.ItemUnitId == unit.Id).SellPrice = ItemExtensions.GetPriceUnitDTO(unit, sellprice);
                        }
                        else
                        {
                            purchase.SalePriceChanges.Add(new() { ItemId = selectedPurchaseItem.Item.Id, SellPrice = ItemExtensions.GetPriceUnitDTO(unit, sellprice), ItemUnitId = unit.Id });
                        }
                    }
                }
                else if (result == false)
                {
                    if (purchase.SalePriceChanges.Any(x => x.ItemId == selectedPurchaseItem.Item.Id && x.ItemUnitId == selectedPurchaseItem.ItemUnit.Id))
                    {
                        purchase.SalePriceChanges.First(x => x.ItemId == selectedPurchaseItem.Item.Id && x.ItemUnitId == selectedPurchaseItem.ItemUnit.Id).SellPrice = SalePriceChange;
                    }
                    else
                    {
                        purchase.SalePriceChanges.Add(new() { ItemId = selectedPurchaseItem.Item.Id, SellPrice = SalePriceChange, ItemUnitId = selectedPurchaseItem.ItemUnit.Id });
                    }
                }
            }

        }
        if (purchase.PurchaseItemDTOs.Any(x => x.ItemUnit.Id == selectedPurchaseItem.ItemUnit.Id && x.Item.Id == selectedPurchaseItem.Item.Id
                                            && x.Exp.GetValueOrDefault().ToShortDateString() == selectedPurchaseItem.Exp.GetValueOrDefault().ToShortDateString()
                                            && x.Price == selectedPurchaseItem.Price && index != purchase.PurchaseItemDTOs.IndexOf(selectedPurchaseItem)))
        {
            _isDeleteMessage = false;
            _message = $" هذا الصنف {selectedPurchaseItem.Item.Name} موجود هل تريد اضافة الكمية؟";
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                purchase.PurchaseItemDTOs.First(x => x.ItemUnit.Id == selectedPurchaseItem.ItemUnit.Id && x.Item.Id == selectedPurchaseItem.Item.Id
                                                    && x.Exp.GetValueOrDefault().ToShortDateString() == selectedPurchaseItem.Exp.GetValueOrDefault().ToShortDateString()
                                                    && x.Price == selectedPurchaseItem.Price)
                    .Quantity += selectedPurchaseItem.Quantity;
                if (index != -2)
                {
                    purchase.PurchaseItemDTOs.RemoveAt(index);
                    index = -2;
                }
            }
            else return;
        }
        else if (index == -2)
        {
            selectedPurchaseItem.PurchaseId = id;
            purchase.PurchaseItemDTOs.Add(selectedPurchaseItem);
        }
        index = -2;
        items.First(x => x.Id == selectedPurchaseItem.Item.Id).Quantity += ItemExtensions.GetQuantityUnitDTO(selectedPurchaseItem.ItemUnit, selectedPurchaseItem.Quantity);
        selectedPurchaseItem = new();
        purchase.Total = purchase.PurchaseItemDTOs.Sum(x => x.Price * x.Quantity);
        ItemForAdd.Text = null;
        await ItemForAdd.FocusAsync();
        ChangeDiscount();
    }
    private void EditItemPurchase(PurchaseItemDTO item)
    {
        if (item.ReturnQuantity > 0)
        {
            _snackbar.Add($"لا يمكن تعديل الصنف بعد استرجاعه", Severity.Error); return;
        }
        selectedPurchaseItem = item;
        index = purchase.PurchaseItemDTOs.IndexOf(item);
    }
    private void DeleteItemPurchase(PurchaseItemDTO item)
    {
        if (item.ReturnQuantity > 0)
        {
            _snackbar.Add($"لا يمكن حذف الصنف بعد استرجاعه", Severity.Error); return;
        }
        purchase.PurchaseItemDTOs.Remove(item);
        items.First(x => x.Id == item.Item.Id).Quantity -= ItemExtensions.GetQuantityUnitDTO(item.ItemUnit, item.Quantity);
        purchase.Total = purchase.PurchaseItemDTOs.Sum(x => x.Price * x.Quantity);
        ChangeDiscount();
    }

    private async Task ChangeItem(ItemCMDTO? Item)
    {
        selectedPurchaseItem.Item = Item;
        selectedPurchaseItem.ItemUnit = null;
        if (selectedPurchaseItem.Item != null)
        {
            selectedPurchaseItem.Price = items.First(x => x.Id == selectedPurchaseItem.Item.Id).CostPrice;
            selectedPurchaseItem.ItemUnit = items.First(x => x.Id == selectedPurchaseItem.Item.Id).ItemUnits.First(x => x.IsBasicUnit);
            _dateExp = items.First(x => x.Id == selectedPurchaseItem.Item.Id).IsHaveExp ? DateTime.Now : null;
        }
    }
    private void ChangeItemUnit(ItemUnitDTO? unit)
    {
        selectedPurchaseItem.ItemUnit = unit;
        selectedPurchaseItem.Price = selectedPurchaseItem.ItemUnit != null ? ItemExtensions.GetPriceUnitDTO(unit, items.First(x => x.Id == selectedPurchaseItem.Item.Id).CostPrice) : 0;
        SalePriceChange = selectedPurchaseItem.ItemUnit != null ? selectedPurchaseItem.ItemUnit.SalePrice : 0;
    }
    async void AddNewItem()
    {
        var parameters = new DialogParameters<UpsertItem>();
        parameters.Add(x => x.id, Guid.Empty);
        var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.Medium, FullWidth = true };
        var result = await DialogService.Show<UpsertItem>("إضافة صنف", parameters, options).Result;
        if ((bool?)result.Data != true)
        {
            await GetItems();
            selectedPurchaseItem.Item = items.Select(x => new ItemCMDTO() { Id = x.Id, Name = x.Name }).Last();
            StateHasChanged();
            await ItemForAdd.FocusAsync();
        }
    }
    async void AddNewClient()
    {
        var parameters = new DialogParameters<UpsertClient>();
        parameters.Add(x => x.id, Guid.Empty);
        var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
        var result = await DialogService.Show<UpsertClient>("إضافة العميل", parameters, options).Result;
        if ((string?)result.Data != null)
        {
            await GetClients();
            purchase.Client = clients.First(x => x.Name == (string)result.Data);
            StateHasChanged();
        }
    }
    async Task AddReceipt(Guid financialId)
    {
        if (_isLoading) return; // منع التنفيذ المتعدد

        try
        {
            SetButtonLoadingState("receipt", true);

            var parameters = new DialogParameters<UpsertReceipt>();
            parameters.Add(x => x.id, Guid.Empty);
            parameters.Add(x => x.financialId, financialId);
            parameters.Add(x => x.Purchase, new PurchaseReceiptDTO() { Id = purchase.Id, Client = purchase.Client, FinalTotal = purchase.FinalTotal, InvoiceNo = purchase.InvoiceNo, Paid = purchase.Paid });
            var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
            var result = await DialogService.Show<UpsertReceipt>("إضافة الإيصال", parameters, options).Result;

            if ((bool?)result.Data == true)
            {
                await GetPurchase(id);
                StateHasChanged();
                ShowSuccessMessage("تم إضافة الإيصال بنجاح");
            }
        }
        catch (Exception ex)
        {
            ShowErrorMessage("حدث خطأ أثناء إضافة الإيصال. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            SetButtonLoadingState("receipt", false);
        }
    }
    void ChangeDiscountValue(decimal discount)
    {
        purchase.DiscountValue = discount;
        ChangeDiscount();
    }
    void ChangeIsDiscount(bool isDiscount)
    {
        purchase.IsDiscountValue = isDiscount;
        ChangeDiscount();
    }
    void ChangeDiscount()
    {
        purchase.FinalTotal = purchase.Total - (purchase.IsDiscountValue ? purchase.DiscountValue : (purchase.Total * purchase.DiscountValue / 100));
        purchase.PurchaseItemDTOs.ForEach(x =>
         {
             x.PriceAfterDiscount = (((x.Quantity * x.Price) / purchase.Total) * purchase.FinalTotal) / x.Quantity;
         });
        StateHasChanged();
    }

    async Task Delete(PurchaseDTO obj)
    {
        if (_isLoading) return; // منع التنفيذ المتعدد

        try
        {
            _isDeleteMessage = true;
            _message = $"هل أنت متأكد من حذف فاتورة رقم {obj.InvoiceNo.ToString()} ؟";
            bool? result = await mbox.ShowAsync();

            if (result == true)
            {
                SetButtonLoadingState("delete", true);

                var response = await _purchase.Delete("Purchases/deletePurchase", obj.Id);
                if (response.State)
                {
                    ShowSuccessMessage("تم الحذف بنجاح");
                }
                else
                {
                    ShowErrorMessage(response.Message);
                }
            }
        }
        catch (Exception ex)
        {
            ShowErrorMessage("حدث خطأ أثناء حذف فاتورة الشراء. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            SetButtonLoadingState("delete", false);
        }
    }

    /// <summary>
    /// تحميل إعدادات المتجر الافتراضية
    /// </summary>
    private async Task LoadDefaultShopSettings()
    {
        try
        {
            // استخدام endpoint الصحيح للحصول على الإعدادات الافتراضية
            var response = await _shopSettings.GetByIdAsync("ShopSettings/getDefaultShopSettings", Guid.Empty);
            if (response.response == null && response.model != null)
            {
                _defaultShopSettings = response.model;
            }
            else
            {
                // محاولة جلب أول إعدادات متاحة إذا لم توجد إعدادات افتراضية
                var allSettingsResponse = await _shopSettings.GetAll("ShopSettings/getAllShopSettings");
                if (allSettingsResponse.response == null && allSettingsResponse.list != null && allSettingsResponse.list.Any())
                {
                    _defaultShopSettings = allSettingsResponse.list.First();
                }
                else
                {
                    // في حالة عدم وجود أي إعدادات، استخدم قيم افتراضية
                    _defaultShopSettings = new ShopSettingsDTO
                    {
                        StoreName = "اسم المتجر",
                        CompanyName = "اسم الشركة",
                        CompanyPhone = "رقم الهاتف",
                        StoreAddress = "عنوان المتجر"
                    };
                }
            }
        }
        catch (Exception ex)
        {
            _snackbar.Add($"خطأ في تحميل إعدادات المتجر: {ex.Message}", Severity.Warning);
            // استخدم قيم افتراضية في حالة الخطأ
            _defaultShopSettings = new ShopSettingsDTO
            {
                StoreName = "اسم المتجر",
                CompanyName = "اسم الشركة",
                CompanyPhone = "رقم الهاتف",
                StoreAddress = "عنوان المتجر"
            };
        }
    }

    /// <summary>
    /// إعادة تحميل إعدادات المتجر لضمان الحصول على أحدث البيانات
    /// </summary>
    private async Task RefreshShopSettings()
    {
        await LoadDefaultShopSettings();
    }

    /// <summary>
    /// طباعة فاتورة الشراء باستخدام Print.js
    /// </summary>
    async Task Print()
    {
        // إعادة تحميل إعدادات المتجر قبل الطباعة لضمان الحصول على أحدث البيانات
        await RefreshShopSettings();
        await PrintWithFormat("auto");
    }

    /// <summary>
    /// طباعة فاتورة الشراء بتنسيق محدد
    /// </summary>
    private async Task PrintWithFormat(string format)
    {
        if (_isLoading) return;

        // التحقق من صحة البيانات قبل الطباعة
        if (!ValidatePurchaseForPrint())
            return;

        try
        {
            SetButtonLoadingState("print", true);
            _printButtonText = GetPrintButtonText(format);
            StateHasChanged();

            // التحقق من تحميل دوال JavaScript
            var isJsReady = await CheckJavaScriptReady();
            if (!isJsReady)
            {
                // محاولة استخدام طريقة بديلة
                await PrintUsingFallbackMethod(format);
                return;
            }

            // إنشاء HTML للفاتورة
            var purchaseHtml = GeneratePurchaseInvoiceHtml(format);

            // استدعاء JavaScript لطباعة الفاتورة
            var printResult = await _iJSRuntime.InvokeAsync<bool>("printReceiptFromBlazor", purchaseHtml, format);

            if (printResult)
            {
                ShowSuccessMessage($"تم إرسال فاتورة الشراء للطباعة بنجاح ({GetFormatDisplayName(format)})");
            }
            else
            {
                ShowErrorMessage("فشل في طباعة الفاتورة. تأكد من إعدادات الطابعة");
            }
        }
        catch (Exception ex)
        {
            ShowErrorMessage($"خطأ في طباعة فاتورة الشراء: {ex.Message}");
        }
        finally
        {
            SetButtonLoadingState("print", false);
            _printButtonText = "طباعة الفاتورة";
            StateHasChanged();
        }
    }

    /// <summary>
    /// التحقق من صحة بيانات الفاتورة قبل الطباعة
    /// </summary>
    private bool ValidatePurchaseForPrint()
    {
        if (purchase.FinalTotal <= 0)
        {
            ShowErrorMessage("لا يمكن طباعة فاتورة بقيمة صفر أو سالبة");
            return false;
        }

        if (purchase.Client == null)
        {
            ShowErrorMessage("يجب تحديد المورد قبل الطباعة");
            return false;
        }

        if (!purchase.PurchaseItemDTOs.Any())
        {
            ShowErrorMessage("لا يمكن طباعة فاتورة بدون أصناف");
            return false;
        }

        if (string.IsNullOrEmpty(_defaultShopSettings?.StoreName))
        {
            _snackbar.Add("تحذير: لم يتم تحميل إعدادات المتجر. سيتم استخدام قيم افتراضية", Severity.Warning);
        }

        return true;
    }

    /// <summary>
    /// معاينة فاتورة الشراء قبل الطباعة
    /// </summary>
    private async Task PreviewPurchase()
    {
        try
        {
            // التحقق من وجود بيانات الفاتورة
            if (!ValidatePurchaseForPrint())
                return;

            // إعادة تحميل إعدادات المتجر قبل المعاينة لضمان الحصول على أحدث البيانات
            await RefreshShopSettings();

            // التحقق من تحميل دوال JavaScript
            var isJsReady = await CheckJavaScriptReady();
            if (!isJsReady)
            {
                // استخدام طريقة بديلة للمعاينة
                await PreviewUsingFallbackMethod();
                return;
            }

            // إنشاء HTML للفاتورة
            var purchaseHtml = GeneratePurchaseInvoiceHtml("preview");

            // استدعاء JavaScript لمعاينة الفاتورة
            var previewResult = await _iJSRuntime.InvokeAsync<bool>("previewReceiptFromBlazor", purchaseHtml, "auto");

            if (!previewResult)
            {
                ShowErrorMessage("فشل في فتح معاينة الفاتورة. تأكد من السماح للنوافذ المنبثقة");
            }
            else
            {
                _snackbar.Add("تم فتح معاينة الفاتورة في نافذة جديدة", Severity.Info);
            }
        }
        catch (Exception ex)
        {
            ShowErrorMessage($"خطأ في معاينة الفاتورة: {ex.Message}");
        }
    }

    /// <summary>
    /// التحقق من تحميل دوال JavaScript المطلوبة
    /// </summary>
    private async Task<bool> CheckJavaScriptReady()
    {
        try
        {
            // التحقق من وجود دالة الطباعة
            var printFunctionExists = await _iJSRuntime.InvokeAsync<bool>("eval", "typeof printReceiptFromBlazor === 'function'");

            // التحقق من وجود دالة المعاينة
            var previewFunctionExists = await _iJSRuntime.InvokeAsync<bool>("eval", "typeof previewReceiptFromBlazor === 'function'");

            return printFunctionExists && previewFunctionExists;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// التنقل إلى الفاتورة السابقة
    /// </summary>
    private async Task NavigateToPreviousInvoice()
    {
        try
        {
            // التأكد من وجود فواتير
            if (!PurchasesNum.Any())
            {
                _snackbar.Add("لا توجد فواتير", Severity.Warning);
                return;
            }

            // إذا كان id ليس Guid.Empty نحاول جلب الفاتورة السابقة
            if (id != Guid.Empty)
            {
                int currentIndex = PurchasesNum.FindIndex(x => x.Id == id);
                if (currentIndex > 0)
                {
                    // الفاتورة السابقة تكون العنصر الذي قبله في القائمة
                    var previousInvoice = PurchasesNum[currentIndex - 1];
                    await GetPurchase(previousInvoice.Id);
                    _snackbar.Add($"تم الانتقال إلى الفاتورة رقم {previousInvoice.InvoiceNo}", Severity.Success);
                }
                else
                {
                    _snackbar.Add("لا توجد فاتورة سابقة", Severity.Info);
                }
            }
            else
            {
                // إذا كان id يساوي Guid.Empty، نجلب آخر فاتورة إن وجدت
                var lastInvoice = PurchasesNum.Last();
                await GetPurchase(lastInvoice.Id);
                _snackbar.Add($"تم الانتقال إلى الفاتورة رقم {lastInvoice.InvoiceNo}", Severity.Success);
            }

            // إعادة التركيز على حقل رقم الفاتورة
            await InvoiceNo.FocusAsync();
        }
        catch (Exception ex)
        {
            _snackbar.Add($"خطأ في التنقل: {ex.Message}", Severity.Error);
        }
        finally
        {
            StateHasChanged();
        }
    }

    /// <summary>
    /// التنقل إلى الفاتورة التالية
    /// </summary>
    private async Task NavigateToNextInvoice()
    {
        try
        {
            // التأكد من وجود فواتير
            if (!PurchasesNum.Any())
            {
                _snackbar.Add("لا توجد فواتير", Severity.Warning);
                return;
            }

            // إذا كان id ليس Guid.Empty نحاول جلب الفاتورة اللاحقة
            if (id != Guid.Empty)
            {
                int currentIndex = PurchasesNum.FindIndex(x => x.Id == id);
                if (currentIndex >= 0 && currentIndex < PurchasesNum.Count - 1)
                {
                    // الفاتورة اللاحقة تكون العنصر الذي يلي الفاتورة الحالية
                    var nextInvoice = PurchasesNum[currentIndex + 1];
                    await GetPurchase(nextInvoice.Id);
                    _snackbar.Add($"تم الانتقال إلى الفاتورة رقم {nextInvoice.InvoiceNo}", Severity.Success);
                }
                else
                {
                    _snackbar.Add("لا توجد فاتورة لاحقة", Severity.Info);
                }
            }
            else
            {
                // إذا كان id يساوي Guid.Empty، نجلب أول فاتورة إن وجدت
                var firstInvoice = PurchasesNum.First();
                await GetPurchase(firstInvoice.Id);
                _snackbar.Add($"تم الانتقال إلى الفاتورة رقم {firstInvoice.InvoiceNo}", Severity.Success);
            }

            // إعادة التركيز على حقل رقم الفاتورة
            await InvoiceNo.FocusAsync();
        }
        catch (Exception ex)
        {
            _snackbar.Add($"خطأ في التنقل: {ex.Message}", Severity.Error);
        }
        finally
        {
            StateHasChanged();
        }
    }

    /// <summary>
    /// التحقق من صحة رقم الفاتورة وضمان عدم السماح بالأرقام السالبة أو الصفر
    /// </summary>
    private void ValidateInvoiceNumber()
    {
        if (purchase.InvoiceNo <= 0)
        {
            purchase.InvoiceNo = 1;
            _snackbar.Add("رقم الفاتورة يجب أن يكون أكبر من الصفر", Severity.Warning);
            StateHasChanged();
        }
    }

    /// <summary>
    /// إنشاء HTML لفاتورة الشراء مع التصميم المحسن - تصميم A4 احترافي
    /// </summary>
    private string GeneratePurchaseInvoiceHtml(string format = "auto")
    {
        var logoUrl = GetFullLogoUrl(_defaultShopSettings?.LogoPath);

        var currentDate = purchase.Date.ToString("yyyy/MM/dd");
        var currentTime = DateTime.Now.ToString("HH:mm");
        var clientName = purchase.Client?.Name ?? "غير محدد";
        var storeName = stores.FirstOrDefault(s => s.Id == purchase.StoreId)?.Name ?? "غير محدد";

        // حساب الإجماليات
        var subtotal = purchase.PurchaseItemDTOs.Sum(item => item.Quantity * item.Price);
        var totalAfterDiscount = purchase.FinalTotal;
        var discountAmount = subtotal - totalAfterDiscount;

        return $@"
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

                body {{
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 0;
                    background: white;
                    font-size: 12px;
                    line-height: 1.3;
                }}

                .invoice-container {{
                    width: 190mm;
                    max-height: 277mm;
                    margin: 0 auto;
                    background: #ffffff;
                    border: 1px solid #1e40af;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    padding: 8mm;
                    page-break-inside: avoid;
                    overflow: hidden;
                }}

                /* الشريط العلوي الأزرق */
                .top-blue-stripe {{
                    height: 4px;
                    background: linear-gradient(90deg, #1e40af 0%, #3b82f6 60%, transparent 100%);
                    width: 60%;
                    margin-bottom: 8px;
                }}

                /* الهيدر */
                .invoice-header {{
                    padding: 10px 15px;
                    position: relative;
                    background: #ffffff;
                    border-bottom: 1px solid #e2e8f0;
                    margin-bottom: 12px;
                    page-break-inside: avoid;
                    min-height: 60px;
                }}

                /* شعار الشركة في الزاوية اليمنى */
                .company-logo {{
                    position: absolute;
                    top: 10px;
                    right: 15px;
                    width: 50px;
                    height: 50px;
                    background: #1e40af;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 10px;
                    font-weight: bold;
                    text-align: center;
                    line-height: 1.1;
                    border-radius: 4px;
                }}

                /* معلومات الشركة في الوسط */
                .company-info {{
                    text-align: center;
                    margin: 0 70px;
                    padding-top: 5px;
                }}

                .store-name {{
                    font-size: 16px;
                    font-weight: 700;
                    color: #1e293b;
                    margin-bottom: 4px;
                }}

                .company-details {{
                    font-size: 10px;
                    color: #64748b;
                    line-height: 1.2;
                }}

                /* التاريخ في الزاوية اليسرى */
                .date-section {{
                    position: absolute;
                    top: 10px;
                    left: 15px;
                    background: #1e40af;
                    color: white;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: 600;
                    text-align: center;
                    min-width: 70px;
                }}

                /* عنوان الفاتورة */
                .invoice-title {{
                    text-align: center;
                    font-size: 18px;
                    font-weight: 700;
                    color: #1e40af;
                    margin: 15px 0 10px 0;
                    padding-bottom: 8px;
                    border-bottom: 2px solid #1e40af;
                    page-break-inside: avoid;
                }}

                .invoice-number {{
                    text-align: center;
                    font-size: 12px;
                    color: #64748b;
                    margin-bottom: 12px;
                }}

                /* معلومات الفاتورة */
                .invoice-info {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    margin-bottom: 12px;
                    padding: 8px;
                    background: #f8fafc;
                    border-radius: 4px;
                    border: 1px solid #e2e8f0;
                    page-break-inside: avoid;
                }}

                .info-item {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 3px 0;
                }}

                .info-label {{
                    font-size: 11px;
                    color: #64748b;
                    font-weight: 500;
                }}

                .info-value {{
                    font-size: 11px;
                    color: #1e293b;
                    font-weight: 600;
                }}

                /* جدول الأصناف */
                .items-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 12px;
                    background: white;
                    border: 1px solid #e2e8f0;
                    page-break-inside: avoid;
                }}

                .items-table th {{
                    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
                    color: white;
                    padding: 6px 4px;
                    text-align: center;
                    font-weight: 600;
                    font-size: 10px;
                    border-bottom: 1px solid #1e40af;
                }}

                .items-table td {{
                    padding: 4px 3px;
                    text-align: center;
                    border-bottom: 1px solid #e2e8f0;
                    font-size: 9px;
                    color: #1e293b;
                    line-height: 1.2;
                }}

                .items-table tr:nth-child(even) {{
                    background: #f8fafc;
                }}

                .items-table tbody {{
                    page-break-inside: auto;
                }}

                .items-table tr {{
                    page-break-inside: avoid;
                }}

                /* قسم الإجماليات */
                .totals-section {{
                    margin-top: 8px;
                    padding: 8px;
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    border-radius: 4px;
                    border: 1px solid #1e40af;
                    page-break-inside: avoid;
                }}

                .totals-grid {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 6px;
                }}

                .total-item {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 4px 8px;
                    background: white;
                    border-radius: 3px;
                    border: 1px solid #d1d5db;
                }}

                .total-label {{
                    font-size: 10px;
                    color: #374151;
                    font-weight: 500;
                }}

                .total-value {{
                    font-size: 10px;
                    color: #1e293b;
                    font-weight: 700;
                }}

                .final-total {{
                    grid-column: 1 / -1;
                    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%) !important;
                    color: white !important;
                    border: none !important;
                }}

                .final-total .total-label,
                .final-total .total-value {{
                    color: white !important;
                    font-weight: 700 !important;
                    font-size: 11px !important;
                }}

                /* الفوتر */
                .invoice-footer {{
                    margin-top: 8px;
                    padding-top: 8px;
                    border-top: 1px solid #e2e8f0;
                    text-align: center;
                    font-size: 9px;
                    color: #64748b;
                    page-break-inside: avoid;
                }}

                /* تنسيقات الطباعة */
                @media print {{
                    * {{
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        box-sizing: border-box !important;
                    }}

                    body {{
                        margin: 0 !important;
                        padding: 0 !important;
                        background: white !important;
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        font-size: 12px !important;
                    }}

                    .invoice-container {{
                        width: 190mm !important;
                        max-height: 277mm !important;
                        height: auto !important;
                        min-height: auto !important;
                        margin: 0 !important;
                        padding: 10mm !important;
                        background: white !important;
                        box-shadow: none !important;
                        border: 1px solid #000000 !important;
                        page-break-after: auto !important;
                        page-break-inside: avoid !important;
                        overflow: hidden !important;
                    }}

                    /* منع كسر الصفحة داخل العناصر المهمة */
                    .invoice-header,
                    .invoice-title,
                    .invoice-info,
                    .totals-section,
                    .invoice-footer {{
                        page-break-inside: avoid !important;
                    }}

                    /* السماح بكسر الصفحة في الجدول إذا لزم الأمر */
                    .items-table {{
                        page-break-inside: auto !important;
                    }}

                    .items-table tr {{
                        page-break-inside: avoid !important;
                    }}

                    /* إخفاء أزرار الطباعة والإغلاق */
                    .mud-button, .mud-fab, .mud-dialog-actions,
                    .print-button, .close-button, .preview-button,
                    button, .btn, [role=""button""] {{
                        display: none !important;
                    }}

                    @page {{
                        size: A4 portrait !important;
                        margin: 10mm !important;
                    }}
                }}

                /* للعرض على الشاشة */
                @media screen {{
                    .invoice-container {{
                        transform: scale(0.8);
                        transform-origin: top center;
                        margin: 20px auto;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    }}
                }}
            </style>

            <div class='invoice-container'>
                <!-- الشريط الأزرق العلوي -->
                <div class='top-blue-stripe'></div>

                <div class='invoice-header'>
                    <!-- شعار الشركة -->
                    <div class='company-logo'>
                        {(!string.IsNullOrEmpty(logoUrl) ? $"<img src='{logoUrl}' alt='شعار' style='width:100%;height:100%;object-fit:contain;' onerror='this.parentElement.innerHTML=\"شعار<br>الشركة\"' />" : "شعار<br>الشركة")}
                    </div>

                    <!-- التاريخ -->
                    <div class='date-section'>
                        التاريخ: {currentDate}
                    </div>

                    <!-- معلومات الشركة -->
                    <div class='company-info'>
                        <div class='store-name'>{_defaultShopSettings?.StoreName ?? "اسم المتجر"}</div>
                        <div class='company-details'>
                            {_defaultShopSettings?.StoreAddress ?? "عنوان المتجر"}<br>
                            هاتف: {_defaultShopSettings?.CompanyPhone ?? "رقم الهاتف"}
                        </div>
                    </div>
                </div>

                <!-- عنوان الفاتورة -->
                <div class='invoice-title'>فاتورة شراء</div>
                <div class='invoice-number'>رقم الفاتورة: {purchase.InvoiceNo}</div>

                <!-- معلومات الفاتورة -->
                <div class='invoice-info'>
                    <div class='info-item'>
                        <span class='info-label'>المورد:</span>
                        <span class='info-value'>{clientName}</span>
                    </div>
                    <div class='info-item'>
                        <span class='info-label'>المخزن:</span>
                        <span class='info-value'>{storeName}</span>
                    </div>
                    <div class='info-item'>
                        <span class='info-label'>تاريخ الفاتورة:</span>
                        <span class='info-value'>{currentDate}</span>
                    </div>
                    <div class='info-item'>
                        <span class='info-label'>وقت الطباعة:</span>
                        <span class='info-value'>{currentTime}</span>
                    </div>
                </div>

                <!-- جدول الأصناف -->
                <table class='items-table'>
                    <thead>
                        <tr>
                            <th style='width: 5%;'>#</th>
                            <th style='width: 25%;'>الصنف</th>
                            <th style='width: 10%;'>الوحدة</th>
                            <th style='width: 10%;'>الكمية</th>
                            <th style='width: 12%;'>السعر</th>
                            <th style='width: 10%;'>التخفيض</th>
                            <th style='width: 12%;'>السعر بعد التخفيض</th>
                            <th style='width: 16%;'>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {string.Join("", purchase.PurchaseItemDTOs.Select((item, index) => $@"
                        <tr>
                            <td>{index + 1}</td>
                            <td style='text-align: right; padding-right: 10px;'>{item.Item?.Name ?? "غير محدد"}</td>
                            <td>{item.ItemUnit?.Unit?.Name ?? "غير محدد"}</td>
                            <td>{item.Quantity:N2}</td>
                            <td>{item.Price:N2}</td>
                            <td>{(item.Price - item.PriceAfterDiscount):N2}</td>
                            <td>{item.PriceAfterDiscount:N2}</td>
                            <td style='font-weight: 600;'>{(item.Quantity * item.PriceAfterDiscount):N2}</td>
                        </tr>"))}
                    </tbody>
                </table>

                <!-- قسم الإجماليات -->
                <div class='totals-section'>
                    <div class='totals-grid'>
                        <div class='total-item'>
                            <span class='total-label'>الإجمالي قبل التخفيض:</span>
                            <span class='total-value'>{subtotal:N2} ريال</span>
                        </div>
                        <div class='total-item'>
                            <span class='total-label'>قيمة التخفيض:</span>
                            <span class='total-value'>{discountAmount:N2} ريال</span>
                        </div>
                        <div class='total-item'>
                            <span class='total-label'>المدفوع:</span>
                            <span class='total-value'>{purchase.Paid:N2} ريال</span>
                        </div>
                        <div class='total-item'>
                            <span class='total-label'>المتبقي:</span>
                            <span class='total-value'>{(purchase.FinalTotal - purchase.Paid):N2} ريال</span>
                        </div>
                        <div class='total-item final-total'>
                            <span class='total-label'>الإجمالي النهائي:</span>
                            <span class='total-value'>{totalAfterDiscount:N2} ريال</span>
                        </div>
                    </div>
                </div>

                <!-- الفوتر -->
                <div class='invoice-footer'>
                    <p>شكراً لتعاملكم معنا</p>
                    <p>{_defaultShopSettings?.CompanyName ?? "اسم الشركة"} - {_defaultShopSettings?.CompanyPhone ?? "رقم الهاتف"}</p>
                </div>
            </div>";
    }

    /// <summary>
    /// طباعة باستخدام طريقة بديلة عند عدم تحميل مكتبة Print.js
    /// </summary>
    private async Task PrintUsingFallbackMethod(string format)
    {
        try
        {
            // إنشاء HTML للفاتورة
            var purchaseHtml = GeneratePurchaseInvoiceHtml(format);

            // إنشاء صفحة HTML كاملة للطباعة
            var fullHtml = $@"
                <!DOCTYPE html>
                <html dir='rtl' lang='ar'>
                <head>
                    <meta charset='UTF-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    <title>طباعة فاتورة الشراء</title>
                    <style>
                        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
                        * {{
                            box-sizing: border-box;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }}
                        body {{
                            font-family: 'Noto Sans Arabic', Arial, sans-serif;
                            direction: rtl;
                            margin: 0;
                            padding: 20px;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }}
                        @media print {{
                            * {{
                                -webkit-print-color-adjust: exact !important;
                                print-color-adjust: exact !important;
                                color-adjust: exact !important;
                            }}
                            body {{ margin: 0 !important; padding: 0 !important; background: white !important; }}
                            @page {{ size: A4 portrait !important; margin: 10mm !important; }}
                        }}
                    </style>
                </head>
                <body>
                    {purchaseHtml}
                    <script>
                        window.onload = function() {{
                            setTimeout(function() {{
                                window.print();
                            }}, 500);
                        }};
                    </script>
                </body>
                </html>";

            // فتح نافذة جديدة للطباعة
            await _iJSRuntime.InvokeVoidAsync("eval", $@"
                var printWindow = window.open('', '_blank', 'width=800,height=600');
                printWindow.document.write(`{fullHtml.Replace("`", "\\`")}`);
                printWindow.document.close();
            ");

            ShowSuccessMessage("تم فتح نافذة الطباعة (طريقة بديلة)");
        }
        catch (Exception ex)
        {
            ShowErrorMessage($"خطأ في الطباعة البديلة: {ex.Message}");
        }
    }

    /// <summary>
    /// معاينة باستخدام طريقة بديلة
    /// </summary>
    private async Task PreviewUsingFallbackMethod()
    {
        try
        {
            // إنشاء HTML للفاتورة
            var purchaseHtml = GeneratePurchaseInvoiceHtml("preview");

            // إنشاء صفحة HTML كاملة للمعاينة
            var fullHtml = $@"
                <!DOCTYPE html>
                <html dir='rtl' lang='ar'>
                <head>
                    <meta charset='UTF-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    <title>معاينة فاتورة الشراء</title>
                    <style>
                        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
                        * {{
                            box-sizing: border-box;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }}
                        body {{
                            font-family: 'Noto Sans Arabic', Arial, sans-serif;
                            direction: rtl;
                            margin: 0;
                            padding: 20px;
                            background: #f5f5f5;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }}
                        .actions {{ text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; }}
                        .btn {{ padding: 10px 20px; margin: 0 5px; border: none; border-radius: 4px; cursor: pointer; font-family: 'Noto Sans Arabic', Arial, sans-serif; }}
                        .btn-print {{ background: #007bff; color: white; }}
                        .btn-close {{ background: #6c757d; color: white; }}
                        @media print {{
                            .actions {{ display: none !important; }}
                            @page {{ size: A4 portrait !important; margin: 10mm !important; }}
                        }}
                    </style>
                </head>
                <body>
                    {purchaseHtml}
                    <div class='actions'>
                        <button class='btn btn-print' onclick='window.print()'>طباعة</button>
                        <button class='btn btn-close' onclick='window.close()'>إغلاق</button>
                    </div>
                </body>
                </html>";

            // فتح نافذة جديدة للمعاينة
            await _iJSRuntime.InvokeVoidAsync("eval", $@"
                var previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
                previewWindow.document.write(`{fullHtml.Replace("`", "\\`")}`);
                previewWindow.document.close();
            ");

            ShowSuccessMessage("تم فتح معاينة الفاتورة (طريقة بديلة)");
        }
        catch (Exception ex)
        {
            ShowErrorMessage($"خطأ في المعاينة البديلة: {ex.Message}");
        }
    }

    /// <summary>
    /// الحصول على نص زر الطباعة حسب التنسيق
    /// </summary>
    private string GetPrintButtonText(string format)
    {
        return "جاري طباعة A4...";
    }

    /// <summary>
    /// الحصول على اسم التنسيق للعرض
    /// </summary>
    private string GetFormatDisplayName(string format)
    {
        return "A4";
    }

    /// <summary>
    /// تحويل مسار الشعار النسبي إلى URL كامل للـ API
    /// </summary>
    /// <param name="logoPath">مسار الشعار النسبي</param>
    /// <returns>URL كامل للصورة</returns>
    private string GetFullLogoUrl(string? logoPath)
    {
        if (string.IsNullOrEmpty(logoPath))
        {
            return string.Empty;
        }

        // إذا كان المسار يبدأ بـ http أو https، فهو مسار كامل
        if (logoPath.StartsWith("http://") || logoPath.StartsWith("https://"))
        {
            return logoPath;
        }

        // إذا كان المسار يبدأ بـ data:، فهو base64
        if (logoPath.StartsWith("data:"))
        {
            return logoPath;
        }

        // URL الأساسي للـ API (يجب أن يتطابق مع إعدادات النظام)
        var apiBaseUrl = "https://localhost:7282";

        // تحويل المسار النسبي إلى URL كامل للـ API
        // مثال: /logo/image.png -> https://localhost:7282/logo/image.png
        var fullPath = logoPath.StartsWith("/") ? logoPath : "/" + logoPath;
        return apiBaseUrl + fullPath;
    }
}