@page "/upsertPurchase/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]
<MudDialog Style="min-width: 100vw;">
    <TitleContent>
        <div class="d-flex align-center gap-3">
            <MudIcon Icon="@Icons.Material.Filled.Receipt" Color="Color.Primary" Size="Size.Large" />
            <div>
                <MudText Typo="Typo.h5" Color="Color.Primary">@(id == Guid.Empty ? "فاتورة شراء جديدة" : "تعديل فاتورة شراء")</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary">@DateTime.Now.ToString("dd-MM-yyyy")</MudText>
            </div>
        </div>

  
    </TitleContent>

    <DialogContent>

        <EditForm Model="@purchase" id="my-form" @onkeydown="@keydownForm" @ref=Form>
            <DataAnnotationsValidator />


            <div Style="display: flex; justify-content: flex-end;">
                <MudPaper Elevation="3" Class="pa-3 d-inline-block" Style="max-width: 600px; margin-right: auto;">
                    <MudGrid Class="small-TextField">

                        <MudItem xs="12" sm="6" md="6" lg="6">
                            <MudDatePicker @bind-Date="_datePurchase"
                                           Label="التاريخ"
                                           Variant="Variant.Outlined"
                                           AdornmentIcon="@Icons.Material.Filled.Event"
                                           Adornment="Adornment.End" />
                        </MudItem>

                        <MudItem xs="12" sm="6" md="6" lg="6">
                            <div class="d-flex align-center gap-2 invoice-navigation">
                                <!-- زر التالي (→) -->
                                <MudIconButton Icon="@Icons.Material.Filled.ArrowForward"
                                               Color="Color.Primary"
                                               Size="Size.Medium"
                                               Variant="Variant.Outlined"
                                               OnClick="NavigateToNextInvoice"
                                               Title="الفاتورة التالية (→)"
                                               Class="navigation-btn flex-shrink-0"
                                               Disabled="@(_isLoading || !PurchasesNum.Any() || (id != Guid.Empty && PurchasesNum.FindIndex(x => x.Id == id) >= PurchasesNum.Count - 1))" />



                               
                                <!-- حقل رقم الفاتورة -->
                                <MudTextField T="int" Value="purchase.InvoiceNo" @ref="InvoiceNo"
                                              Label="رقم الفاتورة" OnKeyDown="KeyDownInvoice"
                                              ValueChanged="@((int value) => { purchase.InvoiceNo = value; ValidateInvoiceNumber(); })"
                                              Min="1"
                                              Variant="Variant.Outlined"
                                              AdornmentIcon="@Icons.Material.Filled.Numbers"
                                              Adornment="Adornment.End"
                                              For="@(() => purchase.InvoiceNo)"
                                              Class="invoice-field flex-grow-1" />

                                <!-- زر السابق (←) -->
                                <MudIconButton Icon="@Icons.Material.Filled.ArrowBack"
                                               Color="Color.Primary"
                                               Size="Size.Medium"
                                               Variant="Variant.Outlined"
                                               OnClick="NavigateToPreviousInvoice"
                                               Title="الفاتورة السابقة (←)"
                                               Class="navigation-btn flex-shrink-0"
                                               Disabled="@(_isLoading || !PurchasesNum.Any() || (id != Guid.Empty && PurchasesNum.FindIndex(x => x.Id == id) <= 0))" />


                          </div>
                        </MudItem>
                    </MudGrid>
                </MudPaper>
            </div>

            <div class="small-TextField">
                <MudDivider />
                <!-- Basic Information Section -->
                <MudPaper Elevation="3" Class="pa-2 d-inline-block" Style="max-width: 400px; margin-left: auto;">
                    <MudGrid>
                        <MudItem xs="6" sm="6" md="6" lg="6">
                            <MudSelect T="Guid?" @bind-Value="purchase.StoreId" Label="المخازن"
                                       Variant="Variant.Outlined"
                                       Adornment="Adornment.End"
                                       Disabled="purchase.Id!=Guid.Empty" AdornmentIcon="@Icons.Material.Filled.Store" Required="true"
                                       RequiredError="يجب اختيار المخزن" For="@(() =>purchase.StoreId)">
                                <MudSelectItem T="Guid?" Value="null">اختيار </MudSelectItem>
                                @foreach (var store in stores)
                                {
                                    <MudSelectItem T="Guid?" Value="store.Id"> @store.Name </MudSelectItem>
                                }
                            </MudSelect>
                        </MudItem>

                        <MudItem xs="6" sm="6" md="6" lg="6">
                            <div class="d-flex gap-2">
                                <MudComboBox @bind-Value="purchase.Client"
                                             Label="المورد" Editable="true"
                                             SearchFunc="@SearchClientFunc"
                                             ToStringFunc="@(e => e?.Name ?? string.Empty)"
                                             Variant="Variant.Outlined"
                                             AdornmentIcon="@Icons.Material.Filled.Person"
                                             Adornment="Adornment.End" For="@(() => purchase.Client)">
                                    @foreach (var client in clients)
                                    {
                                        <MudComboBoxItem Value="@client" Text="@client.Name">@client.Name</MudComboBoxItem>
                                    }
                                </MudComboBox>


                                <MudItem Class="d-flex align-center justify-center">
                                    <MudFab Style="height:36px;width:36px;align-self:end" tabindex=8
                                            StartIcon="@Icons.Material.Filled.Person3"
                                            OnClick="AddNewClient" Color="Color.Info"
                                            Class="ml-auto" />
                                </MudItem>
                            </div>
                        </MudItem>
                    </MudGrid>
                </MudPaper>
                <br />
                <!-- Items Section -->
                <MudPaper Elevation="3" Class="pa-3 " >
                    <MudText Typo="Typo.h6" Color="Color.Primary">الأصناف</MudText>
                    <MudDivider />
                    <MudGrid>


                        <MudItem xs="12" sm="7" md="7" lg="7">
                            <div class="d-flex gap-2">
                                <MudAutocomplete T="ItemCMDTO" @ref="ItemForAdd"
                                                 Value="selectedPurchaseItem.Item"
                                                 ValueChanged="ChangeItem"
                                                 Label="الصنف"
                                                 SearchFunc="@SearchItemsWithPagination"
                                                 ToStringFunc="@(e => e?.Name ?? string.Empty)"
                                                 ResetValueOnEmptyText="true"
                                                 CoerceText="true"
                                                 CoerceValue="false"
                                                 OnKeyDown="SelectItem"
                                                 Variant="Variant.Outlined"
                                                 AdornmentIcon="@(_isLoadingItems ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Inventory)"
                                                 Adornment="Adornment.End"
                                                 DebounceInterval="300"
                                                 MaxItems="null"
                                                 MinCharacters="0"
                                                 ShowProgressIndicator="@_isLoadingItems"
                                                 ProgressIndicatorColor="Color.Primary"
                                                 SelectOnClick="true"
                                                 For="@(() =>selectedPurchaseItem.Item)">
                                    <ItemTemplate Context="item">
                                        <div class="d-flex flex-column">
                                            <MudText Typo="Typo.body2">@item.Name</MudText>
                                            @if (item.ItemNums?.Any() == true)
                                            {
                                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                    باركود: @string.Join(", ", item.ItemNums.Select(x => x.Barcode))
                                                </MudText>
                                            }
                                        </div>
                                    </ItemTemplate>
                                    <MoreItemsTemplate>
                                        @if (_hasMoreItems && !_isLoadingItems)
                                        {
                                            <MudMenuItem OnClick="LoadMoreItems" Class="load-more-item">
                                                <div class="d-flex align-center gap-2">
                                                    <MudIcon Icon="@Icons.Material.Filled.ExpandMore" Size="Size.Small" Color="Color.Primary" />
                                                    <MudText Typo="Typo.body2" Color="Color.Primary">
                                                        تحميل المزيد... (@(_totalItemsCount - _displayedItems.Count) متبقي)
                                                    </MudText>
                                                </div>
                                            </MudMenuItem>
                                        }
                                        else if (_isLoadingItems)
                                        {
                                            <MudMenuItem Disabled="true" Class="loading-item">
                                                <div class="d-flex align-center gap-2">
                                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Color="Color.Primary" />
                                                    <MudText Typo="Typo.body2" Color="Color.Secondary">جاري التحميل...</MudText>
                                                </div>
                                            </MudMenuItem>
                                        }
                                        else if (_displayedItems.Count == 0 && !_isLoadingItems)
                                        {
                                            <MudMenuItem Disabled="true">
                                                <div class="d-flex align-center gap-2">
                                                    <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Small" Color="Color.Secondary" />
                                                    <MudText Typo="Typo.body2" Color="Color.Secondary">لا توجد نتائج</MudText>
                                                </div>
                                            </MudMenuItem>
                                        }
                                    </MoreItemsTemplate>
                                </MudAutocomplete>

                                <MudItem Class="d-flex align-center justify-center">
                                    <MudFab Style="height:36px;width:36px;align-self:end" tabindex=8
                                            StartIcon="@Icons.Material.Filled.AddShoppingCart"
                                            OnClick="AddNewItem" Color="Color.Info"
                                            Class="ml-auto" />
                                </MudItem>
                            </div>
                        </MudItem>


                        <MudItem xs="12" sm="2" md="2" lg="2">
                            <MudSelect T="ItemUnitDTO" @ref="ItemUnit"
                                       Value="selectedPurchaseItem.ItemUnit"
                                       ValueChanged="ChangeItemUnit"
                                       Label="الوحدة"
                                       Variant="Variant.Outlined"
                                       AdornmentIcon="@Icons.Material.Filled.Scale"
                                       Adornment="Adornment.End"
                                       For="@(() =>selectedPurchaseItem.ItemUnit)">
                                <MudSelectItem T="ItemUnitDTO" Value="null">اختر وحدة</MudSelectItem>
                                @if (selectedPurchaseItem.Item != null)
                                {
                                    @foreach (var unit in items.First(x => x.Id == selectedPurchaseItem.Item.Id).ItemUnits)
                                    {
                                        <MudSelectItem T="ItemUnitDTO" Value="@unit">@unit.Unit.Name</MudSelectItem>
                                    }
                                }
                            </MudSelect>
                        </MudItem>


                        <MudItem xs="12" sm="2" md="2" lg="2">
                            <MudDatePicker PickerVariant="PickerVariant.Dialog" Label="تاريخ الصلاحية" Disabled="@(selectedPurchaseItem.Item==null?true:!items.First(x => x.Id == selectedPurchaseItem.Item.Id).IsHaveExp)"
                                           @bind-Date=_dateExp Mask="@(new DateMask("0000-00-00"))"
                                           DateFormat="yyyy-MM-dd" For="@(() =>_dateExp)"
                                           Variant="Variant.Outlined"
                                           AdornmentIcon="@Icons.Material.Filled.Event" />
                        </MudItem>


                        <MudItem xs="12" sm="3" md="3" lg="3">
                            <MudNumericField @bind-Value="selectedPurchaseItem.Quantity"
                                             Label="الكمية"
                                             Variant="Variant.Outlined"
                                             Min="0"
                                             AdornmentIcon="@Icons.Material.Filled.Numbers"
                                             Adornment="Adornment.End" />
                        </MudItem>

                        <MudItem xs="12" sm="3" md="3" lg="3">
                            <MudNumericField @bind-Value="selectedPurchaseItem.Price" Min="0"
                                             Label="سعر شراء"
                                             Variant="Variant.Outlined"
                                             AdornmentIcon="@Icons.Material.Filled.AttachMoney"
                                             Adornment="Adornment.End" />
                        </MudItem>

                        <MudItem xs="12" sm="3" md="3" lg="3">
                            <MudNumericField T="decimal" @bind-Value="SalePriceChange" Min="0"
                                             Label="سعر البيع"
                                             Variant="Variant.Outlined"
                                             AdornmentIcon="@Icons.Material.Filled.PriceCheck"
                                             Adornment="Adornment.End" />

                        </MudItem>

                        <MudItem Class="d-flex align-center">
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="AddItemPurchase"
                                       StartIcon="@Icons.Material.Filled.Add"
                                       FullWidth="true">
                                إضافة الصنف
                            </MudButton>
                        </MudItem>



                    </MudGrid>
                </MudPaper>
                <br />
                <!-- Items Table -->
                <MudPaper Elevation="3" Class="pa-4">
                    <MudTable Items="@purchase.PurchaseItemDTOs"
                              Dense="true"
                              Hover="true"
                              Striped="true"
                              Bordered="true">
                        <HeaderContent>
                            <MudTh>#</MudTh>
                            <MudTh>الصنف</MudTh>
                            <MudTh>الوحدة</MudTh>
                            <MudTh>الصلاحية</MudTh>
                            <MudTh>الكمية</MudTh>
                            <MudTh>السعر</MudTh>
                            <MudTh>السعر بعد التخفيض</MudTh>
                            <MudTh>الإجمالي</MudTh>
                            <MudTh>حدث</MudTh>
                        </HeaderContent>
                        <RowTemplate Context="item">
                            <MudTd DataLabel="#">@(purchase.PurchaseItemDTOs.IndexOf(item) + 1)</MudTd>
                            <MudTd DataLabel="الصنف">@item.Item?.Name</MudTd>
                            <MudTd DataLabel="الوحدة">@item.ItemUnit?.Unit?.Name</MudTd>
                            <MudTd DataLabel="الصلاحية">@(item.Exp is null ? "لا يوجد" : item.Exp.Value.ToShortDateString())</MudTd>
                            <MudTd DataLabel="الكمية">@item.Quantity.ToString("N2")</MudTd>
                            <MudTd DataLabel="السعر">@item.Price.ToString("N2")</MudTd>
                            <MudTd DataLabel="السعر بعد التخفيض">@item.PriceAfterDiscount.ToString("N2")</MudTd>
                            <MudTd DataLabel="الإجمالي">@((item.Quantity * item.PriceAfterDiscount).ToString("N2"))</MudTd>
                            <MudTd DataLabel="حدث">

                                <MudIconButton Size="Size.Small"
                                               Color="Color.Info"
                                               Icon="@Icons.Material.Filled.Edit"
                                               OnClick="@(() => EditItemPurchase(item))" />

                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="@(() => DeleteItemPurchase(item))" />
                            </MudTd>
                        </RowTemplate>
                    </MudTable>
                </MudPaper>
                <br />
                <!-- Totals Section -->
                <MudPaper Elevation="3" Class="pa-4">
                    <MudDivider />
                    <MudText Typo="Typo.h6" Color="Color.Primary">طرق دفع</MudText>
                    <MudGrid Justify="Justify.FlexEnd">
                        <!-- Totals and Payment Fields -->
                        @if (id == Guid.Empty)
                        {
                            <MudItem xs="12" sm="6" md="4" lg="3">
                                <MudSelect T="Guid?"
                                           @bind-Value="purchase.TreasuryId"
                                           Label="الخزينة" For="@(() =>purchase.TreasuryId)"
                                           Variant="Variant.Outlined">
                                    <MudSelectItem T="Guid?" Value="null">اختيار الخزينة</MudSelectItem>
                                    @foreach (var userTreasury in userTreasuries)
                                    {
                                        <MudSelectItem T="Guid?" Value="userTreasury.Id">@userTreasury.Treasury.Name</MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>

                        }


                        <MudItem xs="12" sm="6" md="4" lg="3">
                            <MudTextField @bind-Value="purchase.Total"
                                          Label="الإجمالي"
                                          Variant="Variant.Outlined"
                                          ReadOnly="true"
                                          AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                          Adornment="Adornment.End" />
                        </MudItem>


                        <MudItem xs="5" sm="6" md="4" lg="3">
                            <MudTextField T="decimal" Value="purchase.DiscountValue" ValueChanged="ChangeDiscountValue"
                                          Label="التخفيض"
                                          Variant="Variant.Outlined"
                                          AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                          Adornment="Adornment.End" />
                        </MudItem>

                        <MudItem xs="5" sm="6" md="4" lg="3">
                            <div class="d-flex align-center small-radio-group">
                                <MudRadioGroup T="bool" Value="purchase.IsDiscountValue" ValueChanged="ChangeIsDiscount" Class="d-flex gap-2">
                                    <MudRadio Value="true" Color="Color.Success" Dense="true">
                                        <div class="d-flex gap-2 align-center">
                                            <MudIcon Icon="@Icons.Material.Filled.EventAvailable" Size="Size.Small" />
                                            <span>بالقيمة</span>
                                        </div>
                                    </MudRadio>
                                    <MudRadio Value="false" Color="Color.Error" Dense="true">
                                        <div class="d-flex gap-2 align-center">
                                            <MudIcon Icon="@Icons.Material.Filled.EventBusy" Size="Size.Small" />
                                            <span>بالنسبة</span>
                                        </div>
                                    </MudRadio>
                                </MudRadioGroup>
                            </div>
                        </MudItem>

                        <MudItem xs="12" sm="6" md="4" lg="3">
                            <MudTextField @bind-Value="purchase.FinalTotal"
                                          Label="الإجمالي بعد التخفيض" ReadOnly="true"
                                          Variant="Variant.Outlined"
                                          AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                          Adornment="Adornment.End" />
                        </MudItem>




                        <MudItem xs="12" sm="6" md="4" lg="3">
                            <MudNumericField @bind-Value="purchase.Paid"
                                             Label="المدفوع" Disabled="@(id != Guid.Empty)"
                                             Variant="Variant.Outlined"
                                             AdornmentIcon="@Icons.Material.Filled.Payment"
                                             Adornment="Adornment.End" />
                        </MudItem>
                        <MudItem xs="12" sm="6" md="4" lg="3">
                            <MudTextField Value="@(purchase.FinalTotal - purchase.Paid)"
                                          Label="المتبقي"
                                          Variant="Variant.Outlined"
                                          ReadOnly="true"
                                          AdornmentIcon="@Icons.Material.Filled.AccountBalance"
                                          Adornment="Adornment.End" />
                        </MudItem>
                    </MudGrid>
                </MudPaper>

                <br />
                <MudDivider Class="my-1" />

                <MudCardActions Class="pa-4">
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Error"
                               OnClick="Back"
                               StartIcon="@Icons.Material.Filled.ArrowBack"
                               Disabled="@_isLoading">
                        رجوع
                    </MudButton>
                    <MudSpacer />
                    @if (id != Guid.Empty)
                    {

                        <MudButton Style="align-self: end;" Variant="Variant.Filled" Color="Color.Primary"
                                   OnClick="@(()=>AddReceipt(FinancialId.Purchase))"
                                   EndIcon="@(_isReceiptLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Receipt)"
                                   Class="px-6 ma-2"
                                   Disabled="@_isLoading">
                            @if (_isReceiptLoading)
                            {
                                <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                <MudText style="margin:0px;-webkit-user-select: none;user-select: none; margin-right: 8px;" Align="Align.Center" Typo="Typo.h6">جاري الإضافة...</MudText>
                            }
                            else
                            {
                                <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">اضافة ايصال</MudText>
                            }
                        </MudButton>

                    }

                    <MudButton Variant="Variant.Filled"
                               Color="Color.Success"
                               OnClick="NewPurchase"
                               StartIcon="@(_isNewLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Add)"
                               Disabled="@_isLoading">
                        @if (_isNewLoading)
                        {
                            <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                            <span style="margin-right: 8px;">جاري الإنشاء...</span>
                        }
                        else
                        {
                            <span>جديد (F8)</span>
                        }
                    </MudButton>
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               OnClick="Upsert"
                               StartIcon="@(_isSaveLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Save)"
                               Disabled="@_isLoading">
                        @if (_isSaveLoading)
                        {
                            <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                            <span style="margin-right: 8px;">جاري الحفظ...</span>
                        }
                        else
                        {
                            <span>حفظ (F2)</span>
                        }
                    </MudButton>

                     <MudButton Variant="Variant.Filled"
                               Color="Color.Error"
                               OnClick="@(() => Delete(purchase))"
                               StartIcon="@(_isDeleteLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Delete)"
                               Disabled="@_isLoading">
                        @if (_isDeleteLoading)
                        {
                            <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                            <span style="margin-right: 8px;">جاري الحذف...</span>
                        }
                        else
                        {
                            <span>حذف (Del)</span>
                        }
                    </MudButton>



                    @if (id != Guid.Empty)
                    {
                        <!-- زر معاينة الفاتورة -->
                        <MudButton Variant="Variant.Text"
                                   Color="Color.Info"
                                   OnClick="@PreviewPurchase"
                                   Disabled="@_isPrinting"
                                   StartIcon="@Icons.Material.Filled.Preview"
                                   Class="mx-1">
                            معاينة الفاتورة
                        </MudButton>
                    }

                    <MudButton Variant="Variant.Filled"
                               Color="Color.Surface" OnClick="@Print"
                               StartIcon="@(_isPrintLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Print)"
                               Disabled="@_isLoading">
                        @if (_isPrintLoading)
                        {
                            <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                            <span style="margin-right: 8px;">جاري الطباعة...</span>
                        }
                        else
                        {
                            <span>طباعة</span>
                        }
                    </MudButton>
                </MudCardActions>
            </div> 

            @* رسالة عن طريق بوب *@



            <MudMessageBox @ref="mbox"
                           CancelText="إلغاء"
                           Class="rounded-lg">
                <MessageContent>
                    <div class="d-flex flex-column gap-4">
                        <MudIcon Icon="@(_isDeleteMessage? Icons.Material.Filled.Delete:Icons.Material.Filled.Warning)"
                                 Color="@(_isDeleteMessage? Color.Error:Color.Warning)"
                                 Size="Size.Large"
                                 Class="mx-auto" />
                        <MudText Align="Align.Center">
                            @_message
                        </MudText>
                    </div>
                </MessageContent>
                <YesButton>
                    <MudButton Variant="Variant.Filled"
                               Color="@(_isDeleteMessage? Color.Error:Color.Warning)"
                               StartIcon="@(_isDeleteMessage? Icons.Material.Filled.DeleteForever:Icons.Material.Filled.Info)"
                               Size="Size.Large">
                        تأكيد
                    </MudButton>
                </YesButton>
                <NoButton>
                    <MudButton Variant="Variant.Filled"
                               Color="@(Color.Default)"
                               StartIcon="@(Icons.Material.Filled.Cancel)"
                               Size="Size.Large">
                        إلغاء
                    </MudButton>
                </NoButton>
            </MudMessageBox>
        </EditForm>

     
    </DialogContent>


</MudDialog>



<style>

    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }

    /* تحسينات للتخطيط العربي RTL */
    .invoice-navigation {
        direction: rtl;
    }

    .invoice-navigation .d-flex {
        flex-direction: row-reverse;
    }

    /* تحسين أزرار التنقل */
    .navigation-btn {
        min-width: 40px;
        height: 40px;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .navigation-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .navigation-btn:active {
        transform: scale(0.95);
    }

    /* تحسين حقل رقم الفاتورة */
    .invoice-field {
        margin: 0 8px;
    }

    /* تحسينات للبحث المترقم */
    .load-more-item {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.05) !important;
        border-top: 1px solid rgba(var(--mud-palette-primary-rgb), 0.12);
        transition: background-color 0.2s ease;
    }

    .load-more-item:hover {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.08) !important;
    }

    .loading-item {
        background-color: rgba(var(--mud-palette-surface-rgb), 0.5) !important;
        border-top: 1px solid rgba(var(--mud-palette-divider-rgb), 0.12);
    }

    /* تحسين عرض عناصر البحث */
    .mud-autocomplete .mud-list-item {
        padding: 8px 12px;
        border-bottom: 1px solid rgba(var(--mud-palette-divider-rgb), 0.08);
    }

    .mud-autocomplete .mud-list-item:hover {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.04);
    }

    .mud-autocomplete .mud-list-item:last-child {
        border-bottom: none;
    }
</style>

