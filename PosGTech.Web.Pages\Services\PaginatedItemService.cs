using System.Text.Json;
using PosGTech.ModelsDTO.Items;
using PosGTech.Models.ViewModels;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Services
{
    public class PaginatedItemService : IPaginatedItemService
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;

        public PaginatedItemService(HttpClient httpClient)
        {
            _httpClient = httpClient;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public async Task<(PaginatedItemsDTO? result, ResponseVM? error)> SearchItemsForPurchaseAsync(string? search = null, int page = 1, int pageSize = 50)
        {
            try
            {
                // بناء URL مع المعاملات
                var url = $"Items/searchItemsForPurchase?page={page}&pageSize={pageSize}";
                if (!string.IsNullOrWhiteSpace(search))
                {
                    url += $"&search={Uri.EscapeDataString(search)}";
                }

                var response = await _httpClient.GetAsync(url);
                var content = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    // التحقق من أن المحتوى ليس فارغاً
                    if (string.IsNullOrWhiteSpace(content))
                    {
                        return (null, new ResponseVM { State = false, Message = "استجابة فارغة من الخادم" });
                    }

                    try
                    {
                        var result = JsonSerializer.Deserialize<PaginatedItemsDTO>(content, _jsonOptions);
                        return (result, null);
                    }
                    catch (JsonException jsonEx)
                    {
                        return (null, new ResponseVM { State = false, Message = $"خطأ في تحليل البيانات: {jsonEx.Message}" });
                    }
                }
                else
                {
                    // محاولة تحليل رسالة الخطأ
                    try
                    {
                        if (!string.IsNullOrWhiteSpace(content))
                        {
                            var errorResponse = JsonSerializer.Deserialize<ResponseVM>(content, _jsonOptions);
                            return (null, errorResponse ?? new ResponseVM { State = false, Message = $"خطأ من الخادم: {response.StatusCode}" });
                        }
                    }
                    catch (JsonException)
                    {
                        // إذا فشل تحليل JSON، استخدم رسالة افتراضية
                    }

                    return (null, new ResponseVM { State = false, Message = $"خطأ من الخادم: {response.StatusCode} - {response.ReasonPhrase}" });
                }
            }
            catch (HttpRequestException httpEx)
            {
                return (null, new ResponseVM { State = false, Message = $"خطأ في الشبكة: {httpEx.Message}" });
            }
            catch (TaskCanceledException)
            {
                return (null, new ResponseVM { State = false, Message = "انتهت مهلة الطلب" });
            }
            catch (Exception ex)
            {
                return (null, new ResponseVM { State = false, Message = $"خطأ غير متوقع: {ex.Message}" });
            }
        }
    }
}
