using System.Text.Json;
using PosGTech.ModelsDTO.Items;
using PosGTech.Models.ViewModels;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Services
{
    public class PaginatedItemService : IPaginatedItemService
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;

        public PaginatedItemService(HttpClient httpClient)
        {
            _httpClient = httpClient;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public async Task<(PaginatedItemsDTO? result, ResponseVM? error)> SearchItemsForPurchaseAsync(string? search = null, int page = 1, int pageSize = 50)
        {
            try
            {
                // بناء URL مع المعاملات
                var url = $"api/Items/searchItemsForPurchase?page={page}&pageSize={pageSize}";
                if (!string.IsNullOrWhiteSpace(search))
                {
                    url += $"&search={Uri.EscapeDataString(search)}";
                }

                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<PaginatedItemsDTO>(jsonContent, _jsonOptions);
                    return (result, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(errorContent, _jsonOptions);
                    return (null, errorResponse ?? new ResponseVM { State = false, Message = "حدث خطأ غير متوقع" });
                }
            }
            catch (Exception ex)
            {
                return (null, new ResponseVM { State = false, Message = $"خطأ في الاتصال: {ex.Message}" });
            }
        }
    }
}
