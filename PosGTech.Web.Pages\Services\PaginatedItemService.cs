using System.Text.Json;
using PosGTech.ModelsDTO.Items;
using PosGTech.Models.ViewModels;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Services
{
    public class PaginatedItemService : IPaginatedItemService
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly Dictionary<string, (PaginatedItemsDTO result, DateTime cachedAt)> _cache = new();
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(2);

        public PaginatedItemService(HttpClient httpClient)
        {
            _httpClient = httpClient;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public async Task<(PaginatedItemsDTO? result, ResponseVM? error)> SearchItemsForPurchaseAsync(string? search = null, int page = 1, int pageSize = 50)
        {
            try
            {
                // إنشاء مفتاح التخزين المؤقت
                var cacheKey = $"{search?.Trim() ?? ""}_{page}_{pageSize}";

                // التحقق من التخزين المؤقت
                if (_cache.TryGetValue(cacheKey, out var cachedData))
                {
                    if (DateTime.Now - cachedData.cachedAt < _cacheExpiry)
                    {
                        return (cachedData.result, null);
                    }
                    else
                    {
                        _cache.Remove(cacheKey);
                    }
                }

                // تنظيف التخزين المؤقت القديم
                CleanExpiredCache();

                // بناء URL مع المعاملات
                var url = $"Items/searchItemsForPurchase?page={page}&pageSize={pageSize}";
                if (!string.IsNullOrWhiteSpace(search))
                {
                    url += $"&search={Uri.EscapeDataString(search.Trim())}";
                }

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var response = await _httpClient.GetAsync(url, cts.Token);
                var content = await response.Content.ReadAsStringAsync(cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    // التحقق من أن المحتوى ليس فارغاً
                    if (string.IsNullOrWhiteSpace(content))
                    {
                        return (null, new ResponseVM { State = false, Message = "استجابة فارغة من الخادم" });
                    }

                    try
                    {
                        var result = JsonSerializer.Deserialize<PaginatedItemsDTO>(content, _jsonOptions);

                        // حفظ في التخزين المؤقت
                        if (result != null)
                        {
                            _cache[cacheKey] = (result, DateTime.Now);
                        }

                        return (result, null);
                    }
                    catch (JsonException jsonEx)
                    {
                        return (null, new ResponseVM { State = false, Message = $"خطأ في تحليل البيانات: {jsonEx.Message}" });
                    }
                }
                else
                {
                    // محاولة تحليل رسالة الخطأ
                    try
                    {
                        if (!string.IsNullOrWhiteSpace(content))
                        {
                            var errorResponse = JsonSerializer.Deserialize<ResponseVM>(content, _jsonOptions);
                            return (null, errorResponse ?? new ResponseVM { State = false, Message = $"خطأ من الخادم: {response.StatusCode}" });
                        }
                    }
                    catch (JsonException)
                    {
                        // إذا فشل تحليل JSON، استخدم رسالة افتراضية
                    }

                    return (null, new ResponseVM { State = false, Message = $"خطأ من الخادم: {response.StatusCode} - {response.ReasonPhrase}" });
                }
            }
            catch (HttpRequestException httpEx)
            {
                return (null, new ResponseVM { State = false, Message = $"خطأ في الشبكة: {httpEx.Message}" });
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                return (null, new ResponseVM { State = false, Message = "انتهت مهلة الطلب" });
            }
            catch (TaskCanceledException)
            {
                return (null, new ResponseVM { State = false, Message = "تم إلغاء الطلب" });
            }
            catch (Exception ex)
            {
                return (null, new ResponseVM { State = false, Message = $"خطأ غير متوقع: {ex.Message}" });
            }
        }

        private void CleanExpiredCache()
        {
            var expiredKeys = _cache
                .Where(kvp => DateTime.Now - kvp.Value.cachedAt > _cacheExpiry)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _cache.Remove(key);
            }

            // الحد الأقصى لحجم التخزين المؤقت
            if (_cache.Count > 50)
            {
                var oldestKeys = _cache
                    .OrderBy(kvp => kvp.Value.cachedAt)
                    .Take(_cache.Count - 30)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in oldestKeys)
                {
                    _cache.Remove(key);
                }
            }
        }
    }
}
